{"name": "vr-mobiles", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && node verify-prisma.js && NEXT_DISABLE_STATIC_GENERATION=true next build", "build:static": "prisma generate && node verify-prisma.js && next build", "build:no-static": "prisma generate && node verify-prisma.js && node build-no-static.js", "build:skip": "prisma generate && node verify-prisma.js && node skip-build.js && NEXT_DISABLE_STATIC_GENERATION=true next build && node restore-build.js", "build:vercel": "node prepare-vercel.js && prisma generate && NEXT_DISABLE_STATIC_GENERATION=true next build", "build:dynamic": "prisma generate && node verify-prisma.js && node dynamic-build.js", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:seed": "node prisma/seed.js", "vercel-build": "node prepare-vercel.js && prisma generate && NEXT_DISABLE_STATIC_GENERATION=true next build", "analyze": "ANALYZE=true next build", "migrate:dev": "prisma migrate dev", "migrate:reset": "prisma migrate reset", "migrate:deploy": "prisma migrate deploy", "postinstall": "prisma generate", "prisma:generate": "prisma generate", "prisma:verify": "node verify-prisma.js", "prisma:init": "node prisma-init.js", "prisma:vercel": "node prisma-vercel.js", "prepare-vercel": "node prepare-vercel.js"}, "dependencies": {"@headlessui/react": "^2.2.2", "@prisma/client": "^6.7.0", "autoprefixer": "^10.4.21", "bcrypt": "^5.1.1", "canvas": "^3.1.0", "critters": "^0.0.23", "dotenv": "^17.0.0", "lodash": "^4.17.21", "next": "15.3.2", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "nprogress": "^0.2.0", "pg": "^8.16.3", "postcss": "^8.5.3", "prisma": "^6.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "tailwindcss": "^3.4.0", "zod": "^3.24.4"}, "optionalDependencies": {"sharp": "^0.33.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.3.2"}}