generator client {
  provider      = "prisma-client-js"
  // Default output path (no need to specify)
  binaryTargets = ["native", "rhel-openssl-1.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int       @id @default(autoincrement())
  username  String    @unique
  password  String
  name      String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  jobCards  JobCard[]
}

model Customer {
  id            Int       @id @default(autoincrement())
  name          String
  mobileNumber  String    @unique
  address       String?
  aadhaarNumber String?
  visitCount    Int       @default(1)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  jobCards      JobCard[]
}

model JobCard {
  id            Int       @id @default(autoincrement())
  billNo        Int?      @unique
  date          DateTime  @default(now())
  isOn          Boolean   @default(false)
  isOff         Boolean   @default(false)
  hasBattery    Boolean   @default(false)
  hasDoor       <PERSON>an   @default(false)
  hasS<PERSON>   @default(false)
  hasSlot       <PERSON>an   @default(false)
  customerName  String
  address       String?
  mobileNumber  String
  complaint     String
  model         String
  admissionFees Float?
  aadhaarNumber String?
  estimate      Float?
  advance       Float?
  finalAmount   Float?
  status        String    @default("pending")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  userId        Int
  createdBy     User      @relation(fields: [userId], references: [id])
  customerId    Int?
  customer      Customer? @relation(fields: [customerId], references: [id])

  // Add indexes for better performance with relationMode = "prisma"
  @@index([userId])
  @@index([customerId])
}
