generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x"]
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model User {
  id        Int       @id @default(autoincrement())
  username  String    @unique
  password  String
  name      String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  jobCards  JobCard[]
}

model JobCard {
  id                Int       @id @default(autoincrement())
  billNumber        String    @unique
  customerName      String
  contactNumber     String
  deviceModel       String
  deviceBrand       String
  deviceColor       String?
  devicePassword    String?
  deviceCondition   String?
  deviceIssue       String
  deviceAccessories String?
  status            String    @default("pending")
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  userId            Int
  user              User      @relation(fields: [userId], references: [id])
  powerOn           Boolean   @default(false)
  powerOff          Boolean   @default(false)
  advanceAmount     Float?
  finalAmount       Float?
  remarks           String?

  @@index([userId])
}
