
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Customer
 * 
 */
export type Customer = $Result.DefaultSelection<Prisma.$CustomerPayload>
/**
 * Model JobCard
 * 
 */
export type JobCard = $Result.DefaultSelection<Prisma.$JobCardPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.customer`: Exposes CRUD operations for the **Customer** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Customers
    * const customers = await prisma.customer.findMany()
    * ```
    */
  get customer(): Prisma.CustomerDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.jobCard`: Exposes CRUD operations for the **JobCard** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more JobCards
    * const jobCards = await prisma.jobCard.findMany()
    * ```
    */
  get jobCard(): Prisma.JobCardDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.7.0
   * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    Customer: 'Customer',
    JobCard: 'JobCard'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "user" | "customer" | "jobCard"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Customer: {
        payload: Prisma.$CustomerPayload<ExtArgs>
        fields: Prisma.CustomerFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CustomerFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CustomerFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload>
          }
          findFirst: {
            args: Prisma.CustomerFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CustomerFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload>
          }
          findMany: {
            args: Prisma.CustomerFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload>[]
          }
          create: {
            args: Prisma.CustomerCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload>
          }
          createMany: {
            args: Prisma.CustomerCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.CustomerDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload>
          }
          update: {
            args: Prisma.CustomerUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload>
          }
          deleteMany: {
            args: Prisma.CustomerDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CustomerUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.CustomerUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomerPayload>
          }
          aggregate: {
            args: Prisma.CustomerAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCustomer>
          }
          groupBy: {
            args: Prisma.CustomerGroupByArgs<ExtArgs>
            result: $Utils.Optional<CustomerGroupByOutputType>[]
          }
          count: {
            args: Prisma.CustomerCountArgs<ExtArgs>
            result: $Utils.Optional<CustomerCountAggregateOutputType> | number
          }
        }
      }
      JobCard: {
        payload: Prisma.$JobCardPayload<ExtArgs>
        fields: Prisma.JobCardFieldRefs
        operations: {
          findUnique: {
            args: Prisma.JobCardFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.JobCardFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload>
          }
          findFirst: {
            args: Prisma.JobCardFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.JobCardFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload>
          }
          findMany: {
            args: Prisma.JobCardFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload>[]
          }
          create: {
            args: Prisma.JobCardCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload>
          }
          createMany: {
            args: Prisma.JobCardCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          delete: {
            args: Prisma.JobCardDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload>
          }
          update: {
            args: Prisma.JobCardUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload>
          }
          deleteMany: {
            args: Prisma.JobCardDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.JobCardUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.JobCardUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$JobCardPayload>
          }
          aggregate: {
            args: Prisma.JobCardAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateJobCard>
          }
          groupBy: {
            args: Prisma.JobCardGroupByArgs<ExtArgs>
            result: $Utils.Optional<JobCardGroupByOutputType>[]
          }
          count: {
            args: Prisma.JobCardCountArgs<ExtArgs>
            result: $Utils.Optional<JobCardCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    user?: UserOmit
    customer?: CustomerOmit
    jobCard?: JobCardOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    jobCards: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    jobCards?: boolean | UserCountOutputTypeCountJobCardsArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountJobCardsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: JobCardWhereInput
  }


  /**
   * Count Type CustomerCountOutputType
   */

  export type CustomerCountOutputType = {
    jobCards: number
  }

  export type CustomerCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    jobCards?: boolean | CustomerCountOutputTypeCountJobCardsArgs
  }

  // Custom InputTypes
  /**
   * CustomerCountOutputType without action
   */
  export type CustomerCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomerCountOutputType
     */
    select?: CustomerCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * CustomerCountOutputType without action
   */
  export type CustomerCountOutputTypeCountJobCardsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: JobCardWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserAvgAggregateOutputType = {
    id: number | null
  }

  export type UserSumAggregateOutputType = {
    id: number | null
  }

  export type UserMinAggregateOutputType = {
    id: number | null
    username: string | null
    password: string | null
    name: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: number | null
    username: string | null
    password: string | null
    name: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    username: number
    password: number
    name: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserAvgAggregateInputType = {
    id?: true
  }

  export type UserSumAggregateInputType = {
    id?: true
  }

  export type UserMinAggregateInputType = {
    id?: true
    username?: true
    password?: true
    name?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    username?: true
    password?: true
    name?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    username?: true
    password?: true
    name?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: UserAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: UserSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _avg?: UserAvgAggregateInputType
    _sum?: UserSumAggregateInputType
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: number
    username: string
    password: string
    name: string | null
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    password?: boolean
    name?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    jobCards?: boolean | User$jobCardsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>



  export type UserSelectScalar = {
    id?: boolean
    username?: boolean
    password?: boolean
    name?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "username" | "password" | "name" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    jobCards?: boolean | User$jobCardsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      jobCards: Prisma.$JobCardPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      username: string
      password: string
      name: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    jobCards<T extends User$jobCardsArgs<ExtArgs> = {}>(args?: Subset<T, User$jobCardsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'Int'>
    readonly username: FieldRef<"User", 'String'>
    readonly password: FieldRef<"User", 'String'>
    readonly name: FieldRef<"User", 'String'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.jobCards
   */
  export type User$jobCardsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    where?: JobCardWhereInput
    orderBy?: JobCardOrderByWithRelationInput | JobCardOrderByWithRelationInput[]
    cursor?: JobCardWhereUniqueInput
    take?: number
    skip?: number
    distinct?: JobCardScalarFieldEnum | JobCardScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Customer
   */

  export type AggregateCustomer = {
    _count: CustomerCountAggregateOutputType | null
    _avg: CustomerAvgAggregateOutputType | null
    _sum: CustomerSumAggregateOutputType | null
    _min: CustomerMinAggregateOutputType | null
    _max: CustomerMaxAggregateOutputType | null
  }

  export type CustomerAvgAggregateOutputType = {
    id: number | null
    visitCount: number | null
  }

  export type CustomerSumAggregateOutputType = {
    id: number | null
    visitCount: number | null
  }

  export type CustomerMinAggregateOutputType = {
    id: number | null
    name: string | null
    mobileNumber: string | null
    address: string | null
    aadhaarNumber: string | null
    visitCount: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type CustomerMaxAggregateOutputType = {
    id: number | null
    name: string | null
    mobileNumber: string | null
    address: string | null
    aadhaarNumber: string | null
    visitCount: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type CustomerCountAggregateOutputType = {
    id: number
    name: number
    mobileNumber: number
    address: number
    aadhaarNumber: number
    visitCount: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type CustomerAvgAggregateInputType = {
    id?: true
    visitCount?: true
  }

  export type CustomerSumAggregateInputType = {
    id?: true
    visitCount?: true
  }

  export type CustomerMinAggregateInputType = {
    id?: true
    name?: true
    mobileNumber?: true
    address?: true
    aadhaarNumber?: true
    visitCount?: true
    createdAt?: true
    updatedAt?: true
  }

  export type CustomerMaxAggregateInputType = {
    id?: true
    name?: true
    mobileNumber?: true
    address?: true
    aadhaarNumber?: true
    visitCount?: true
    createdAt?: true
    updatedAt?: true
  }

  export type CustomerCountAggregateInputType = {
    id?: true
    name?: true
    mobileNumber?: true
    address?: true
    aadhaarNumber?: true
    visitCount?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type CustomerAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Customer to aggregate.
     */
    where?: CustomerWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Customers to fetch.
     */
    orderBy?: CustomerOrderByWithRelationInput | CustomerOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CustomerWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Customers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Customers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Customers
    **/
    _count?: true | CustomerCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: CustomerAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: CustomerSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CustomerMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CustomerMaxAggregateInputType
  }

  export type GetCustomerAggregateType<T extends CustomerAggregateArgs> = {
        [P in keyof T & keyof AggregateCustomer]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCustomer[P]>
      : GetScalarType<T[P], AggregateCustomer[P]>
  }




  export type CustomerGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CustomerWhereInput
    orderBy?: CustomerOrderByWithAggregationInput | CustomerOrderByWithAggregationInput[]
    by: CustomerScalarFieldEnum[] | CustomerScalarFieldEnum
    having?: CustomerScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CustomerCountAggregateInputType | true
    _avg?: CustomerAvgAggregateInputType
    _sum?: CustomerSumAggregateInputType
    _min?: CustomerMinAggregateInputType
    _max?: CustomerMaxAggregateInputType
  }

  export type CustomerGroupByOutputType = {
    id: number
    name: string
    mobileNumber: string
    address: string | null
    aadhaarNumber: string | null
    visitCount: number
    createdAt: Date
    updatedAt: Date
    _count: CustomerCountAggregateOutputType | null
    _avg: CustomerAvgAggregateOutputType | null
    _sum: CustomerSumAggregateOutputType | null
    _min: CustomerMinAggregateOutputType | null
    _max: CustomerMaxAggregateOutputType | null
  }

  type GetCustomerGroupByPayload<T extends CustomerGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CustomerGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CustomerGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CustomerGroupByOutputType[P]>
            : GetScalarType<T[P], CustomerGroupByOutputType[P]>
        }
      >
    >


  export type CustomerSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    mobileNumber?: boolean
    address?: boolean
    aadhaarNumber?: boolean
    visitCount?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    jobCards?: boolean | Customer$jobCardsArgs<ExtArgs>
    _count?: boolean | CustomerCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["customer"]>



  export type CustomerSelectScalar = {
    id?: boolean
    name?: boolean
    mobileNumber?: boolean
    address?: boolean
    aadhaarNumber?: boolean
    visitCount?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type CustomerOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "name" | "mobileNumber" | "address" | "aadhaarNumber" | "visitCount" | "createdAt" | "updatedAt", ExtArgs["result"]["customer"]>
  export type CustomerInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    jobCards?: boolean | Customer$jobCardsArgs<ExtArgs>
    _count?: boolean | CustomerCountOutputTypeDefaultArgs<ExtArgs>
  }

  export type $CustomerPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Customer"
    objects: {
      jobCards: Prisma.$JobCardPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      name: string
      mobileNumber: string
      address: string | null
      aadhaarNumber: string | null
      visitCount: number
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["customer"]>
    composites: {}
  }

  type CustomerGetPayload<S extends boolean | null | undefined | CustomerDefaultArgs> = $Result.GetResult<Prisma.$CustomerPayload, S>

  type CustomerCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<CustomerFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: CustomerCountAggregateInputType | true
    }

  export interface CustomerDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Customer'], meta: { name: 'Customer' } }
    /**
     * Find zero or one Customer that matches the filter.
     * @param {CustomerFindUniqueArgs} args - Arguments to find a Customer
     * @example
     * // Get one Customer
     * const customer = await prisma.customer.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CustomerFindUniqueArgs>(args: SelectSubset<T, CustomerFindUniqueArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Customer that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {CustomerFindUniqueOrThrowArgs} args - Arguments to find a Customer
     * @example
     * // Get one Customer
     * const customer = await prisma.customer.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CustomerFindUniqueOrThrowArgs>(args: SelectSubset<T, CustomerFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Customer that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomerFindFirstArgs} args - Arguments to find a Customer
     * @example
     * // Get one Customer
     * const customer = await prisma.customer.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CustomerFindFirstArgs>(args?: SelectSubset<T, CustomerFindFirstArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Customer that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomerFindFirstOrThrowArgs} args - Arguments to find a Customer
     * @example
     * // Get one Customer
     * const customer = await prisma.customer.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CustomerFindFirstOrThrowArgs>(args?: SelectSubset<T, CustomerFindFirstOrThrowArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Customers that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomerFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Customers
     * const customers = await prisma.customer.findMany()
     * 
     * // Get first 10 Customers
     * const customers = await prisma.customer.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const customerWithIdOnly = await prisma.customer.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CustomerFindManyArgs>(args?: SelectSubset<T, CustomerFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Customer.
     * @param {CustomerCreateArgs} args - Arguments to create a Customer.
     * @example
     * // Create one Customer
     * const Customer = await prisma.customer.create({
     *   data: {
     *     // ... data to create a Customer
     *   }
     * })
     * 
     */
    create<T extends CustomerCreateArgs>(args: SelectSubset<T, CustomerCreateArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Customers.
     * @param {CustomerCreateManyArgs} args - Arguments to create many Customers.
     * @example
     * // Create many Customers
     * const customer = await prisma.customer.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CustomerCreateManyArgs>(args?: SelectSubset<T, CustomerCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a Customer.
     * @param {CustomerDeleteArgs} args - Arguments to delete one Customer.
     * @example
     * // Delete one Customer
     * const Customer = await prisma.customer.delete({
     *   where: {
     *     // ... filter to delete one Customer
     *   }
     * })
     * 
     */
    delete<T extends CustomerDeleteArgs>(args: SelectSubset<T, CustomerDeleteArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Customer.
     * @param {CustomerUpdateArgs} args - Arguments to update one Customer.
     * @example
     * // Update one Customer
     * const customer = await prisma.customer.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CustomerUpdateArgs>(args: SelectSubset<T, CustomerUpdateArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Customers.
     * @param {CustomerDeleteManyArgs} args - Arguments to filter Customers to delete.
     * @example
     * // Delete a few Customers
     * const { count } = await prisma.customer.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CustomerDeleteManyArgs>(args?: SelectSubset<T, CustomerDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Customers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomerUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Customers
     * const customer = await prisma.customer.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CustomerUpdateManyArgs>(args: SelectSubset<T, CustomerUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Customer.
     * @param {CustomerUpsertArgs} args - Arguments to update or create a Customer.
     * @example
     * // Update or create a Customer
     * const customer = await prisma.customer.upsert({
     *   create: {
     *     // ... data to create a Customer
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Customer we want to update
     *   }
     * })
     */
    upsert<T extends CustomerUpsertArgs>(args: SelectSubset<T, CustomerUpsertArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Customers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomerCountArgs} args - Arguments to filter Customers to count.
     * @example
     * // Count the number of Customers
     * const count = await prisma.customer.count({
     *   where: {
     *     // ... the filter for the Customers we want to count
     *   }
     * })
    **/
    count<T extends CustomerCountArgs>(
      args?: Subset<T, CustomerCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CustomerCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Customer.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomerAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CustomerAggregateArgs>(args: Subset<T, CustomerAggregateArgs>): Prisma.PrismaPromise<GetCustomerAggregateType<T>>

    /**
     * Group by Customer.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomerGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CustomerGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CustomerGroupByArgs['orderBy'] }
        : { orderBy?: CustomerGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CustomerGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCustomerGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Customer model
   */
  readonly fields: CustomerFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Customer.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CustomerClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    jobCards<T extends Customer$jobCardsArgs<ExtArgs> = {}>(args?: Subset<T, Customer$jobCardsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Customer model
   */
  interface CustomerFieldRefs {
    readonly id: FieldRef<"Customer", 'Int'>
    readonly name: FieldRef<"Customer", 'String'>
    readonly mobileNumber: FieldRef<"Customer", 'String'>
    readonly address: FieldRef<"Customer", 'String'>
    readonly aadhaarNumber: FieldRef<"Customer", 'String'>
    readonly visitCount: FieldRef<"Customer", 'Int'>
    readonly createdAt: FieldRef<"Customer", 'DateTime'>
    readonly updatedAt: FieldRef<"Customer", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Customer findUnique
   */
  export type CustomerFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * Filter, which Customer to fetch.
     */
    where: CustomerWhereUniqueInput
  }

  /**
   * Customer findUniqueOrThrow
   */
  export type CustomerFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * Filter, which Customer to fetch.
     */
    where: CustomerWhereUniqueInput
  }

  /**
   * Customer findFirst
   */
  export type CustomerFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * Filter, which Customer to fetch.
     */
    where?: CustomerWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Customers to fetch.
     */
    orderBy?: CustomerOrderByWithRelationInput | CustomerOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Customers.
     */
    cursor?: CustomerWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Customers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Customers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Customers.
     */
    distinct?: CustomerScalarFieldEnum | CustomerScalarFieldEnum[]
  }

  /**
   * Customer findFirstOrThrow
   */
  export type CustomerFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * Filter, which Customer to fetch.
     */
    where?: CustomerWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Customers to fetch.
     */
    orderBy?: CustomerOrderByWithRelationInput | CustomerOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Customers.
     */
    cursor?: CustomerWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Customers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Customers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Customers.
     */
    distinct?: CustomerScalarFieldEnum | CustomerScalarFieldEnum[]
  }

  /**
   * Customer findMany
   */
  export type CustomerFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * Filter, which Customers to fetch.
     */
    where?: CustomerWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Customers to fetch.
     */
    orderBy?: CustomerOrderByWithRelationInput | CustomerOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Customers.
     */
    cursor?: CustomerWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Customers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Customers.
     */
    skip?: number
    distinct?: CustomerScalarFieldEnum | CustomerScalarFieldEnum[]
  }

  /**
   * Customer create
   */
  export type CustomerCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * The data needed to create a Customer.
     */
    data: XOR<CustomerCreateInput, CustomerUncheckedCreateInput>
  }

  /**
   * Customer createMany
   */
  export type CustomerCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Customers.
     */
    data: CustomerCreateManyInput | CustomerCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Customer update
   */
  export type CustomerUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * The data needed to update a Customer.
     */
    data: XOR<CustomerUpdateInput, CustomerUncheckedUpdateInput>
    /**
     * Choose, which Customer to update.
     */
    where: CustomerWhereUniqueInput
  }

  /**
   * Customer updateMany
   */
  export type CustomerUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Customers.
     */
    data: XOR<CustomerUpdateManyMutationInput, CustomerUncheckedUpdateManyInput>
    /**
     * Filter which Customers to update
     */
    where?: CustomerWhereInput
    /**
     * Limit how many Customers to update.
     */
    limit?: number
  }

  /**
   * Customer upsert
   */
  export type CustomerUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * The filter to search for the Customer to update in case it exists.
     */
    where: CustomerWhereUniqueInput
    /**
     * In case the Customer found by the `where` argument doesn't exist, create a new Customer with this data.
     */
    create: XOR<CustomerCreateInput, CustomerUncheckedCreateInput>
    /**
     * In case the Customer was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CustomerUpdateInput, CustomerUncheckedUpdateInput>
  }

  /**
   * Customer delete
   */
  export type CustomerDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    /**
     * Filter which Customer to delete.
     */
    where: CustomerWhereUniqueInput
  }

  /**
   * Customer deleteMany
   */
  export type CustomerDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Customers to delete
     */
    where?: CustomerWhereInput
    /**
     * Limit how many Customers to delete.
     */
    limit?: number
  }

  /**
   * Customer.jobCards
   */
  export type Customer$jobCardsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    where?: JobCardWhereInput
    orderBy?: JobCardOrderByWithRelationInput | JobCardOrderByWithRelationInput[]
    cursor?: JobCardWhereUniqueInput
    take?: number
    skip?: number
    distinct?: JobCardScalarFieldEnum | JobCardScalarFieldEnum[]
  }

  /**
   * Customer without action
   */
  export type CustomerDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
  }


  /**
   * Model JobCard
   */

  export type AggregateJobCard = {
    _count: JobCardCountAggregateOutputType | null
    _avg: JobCardAvgAggregateOutputType | null
    _sum: JobCardSumAggregateOutputType | null
    _min: JobCardMinAggregateOutputType | null
    _max: JobCardMaxAggregateOutputType | null
  }

  export type JobCardAvgAggregateOutputType = {
    id: number | null
    billNo: number | null
    admissionFees: number | null
    estimate: number | null
    advance: number | null
    finalAmount: number | null
    userId: number | null
    customerId: number | null
  }

  export type JobCardSumAggregateOutputType = {
    id: number | null
    billNo: number | null
    admissionFees: number | null
    estimate: number | null
    advance: number | null
    finalAmount: number | null
    userId: number | null
    customerId: number | null
  }

  export type JobCardMinAggregateOutputType = {
    id: number | null
    billNo: number | null
    date: Date | null
    isOn: boolean | null
    isOff: boolean | null
    hasBattery: boolean | null
    hasDoor: boolean | null
    hasSim: boolean | null
    hasSlot: boolean | null
    customerName: string | null
    address: string | null
    mobileNumber: string | null
    complaint: string | null
    model: string | null
    admissionFees: number | null
    aadhaarNumber: string | null
    estimate: number | null
    advance: number | null
    finalAmount: number | null
    status: string | null
    createdAt: Date | null
    updatedAt: Date | null
    userId: number | null
    customerId: number | null
  }

  export type JobCardMaxAggregateOutputType = {
    id: number | null
    billNo: number | null
    date: Date | null
    isOn: boolean | null
    isOff: boolean | null
    hasBattery: boolean | null
    hasDoor: boolean | null
    hasSim: boolean | null
    hasSlot: boolean | null
    customerName: string | null
    address: string | null
    mobileNumber: string | null
    complaint: string | null
    model: string | null
    admissionFees: number | null
    aadhaarNumber: string | null
    estimate: number | null
    advance: number | null
    finalAmount: number | null
    status: string | null
    createdAt: Date | null
    updatedAt: Date | null
    userId: number | null
    customerId: number | null
  }

  export type JobCardCountAggregateOutputType = {
    id: number
    billNo: number
    date: number
    isOn: number
    isOff: number
    hasBattery: number
    hasDoor: number
    hasSim: number
    hasSlot: number
    customerName: number
    address: number
    mobileNumber: number
    complaint: number
    model: number
    admissionFees: number
    aadhaarNumber: number
    estimate: number
    advance: number
    finalAmount: number
    status: number
    createdAt: number
    updatedAt: number
    userId: number
    customerId: number
    _all: number
  }


  export type JobCardAvgAggregateInputType = {
    id?: true
    billNo?: true
    admissionFees?: true
    estimate?: true
    advance?: true
    finalAmount?: true
    userId?: true
    customerId?: true
  }

  export type JobCardSumAggregateInputType = {
    id?: true
    billNo?: true
    admissionFees?: true
    estimate?: true
    advance?: true
    finalAmount?: true
    userId?: true
    customerId?: true
  }

  export type JobCardMinAggregateInputType = {
    id?: true
    billNo?: true
    date?: true
    isOn?: true
    isOff?: true
    hasBattery?: true
    hasDoor?: true
    hasSim?: true
    hasSlot?: true
    customerName?: true
    address?: true
    mobileNumber?: true
    complaint?: true
    model?: true
    admissionFees?: true
    aadhaarNumber?: true
    estimate?: true
    advance?: true
    finalAmount?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
    customerId?: true
  }

  export type JobCardMaxAggregateInputType = {
    id?: true
    billNo?: true
    date?: true
    isOn?: true
    isOff?: true
    hasBattery?: true
    hasDoor?: true
    hasSim?: true
    hasSlot?: true
    customerName?: true
    address?: true
    mobileNumber?: true
    complaint?: true
    model?: true
    admissionFees?: true
    aadhaarNumber?: true
    estimate?: true
    advance?: true
    finalAmount?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
    customerId?: true
  }

  export type JobCardCountAggregateInputType = {
    id?: true
    billNo?: true
    date?: true
    isOn?: true
    isOff?: true
    hasBattery?: true
    hasDoor?: true
    hasSim?: true
    hasSlot?: true
    customerName?: true
    address?: true
    mobileNumber?: true
    complaint?: true
    model?: true
    admissionFees?: true
    aadhaarNumber?: true
    estimate?: true
    advance?: true
    finalAmount?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
    customerId?: true
    _all?: true
  }

  export type JobCardAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which JobCard to aggregate.
     */
    where?: JobCardWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of JobCards to fetch.
     */
    orderBy?: JobCardOrderByWithRelationInput | JobCardOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: JobCardWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` JobCards from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` JobCards.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned JobCards
    **/
    _count?: true | JobCardCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: JobCardAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: JobCardSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: JobCardMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: JobCardMaxAggregateInputType
  }

  export type GetJobCardAggregateType<T extends JobCardAggregateArgs> = {
        [P in keyof T & keyof AggregateJobCard]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateJobCard[P]>
      : GetScalarType<T[P], AggregateJobCard[P]>
  }




  export type JobCardGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: JobCardWhereInput
    orderBy?: JobCardOrderByWithAggregationInput | JobCardOrderByWithAggregationInput[]
    by: JobCardScalarFieldEnum[] | JobCardScalarFieldEnum
    having?: JobCardScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: JobCardCountAggregateInputType | true
    _avg?: JobCardAvgAggregateInputType
    _sum?: JobCardSumAggregateInputType
    _min?: JobCardMinAggregateInputType
    _max?: JobCardMaxAggregateInputType
  }

  export type JobCardGroupByOutputType = {
    id: number
    billNo: number | null
    date: Date
    isOn: boolean
    isOff: boolean
    hasBattery: boolean
    hasDoor: boolean
    hasSim: boolean
    hasSlot: boolean
    customerName: string
    address: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees: number | null
    aadhaarNumber: string | null
    estimate: number | null
    advance: number | null
    finalAmount: number | null
    status: string
    createdAt: Date
    updatedAt: Date
    userId: number
    customerId: number | null
    _count: JobCardCountAggregateOutputType | null
    _avg: JobCardAvgAggregateOutputType | null
    _sum: JobCardSumAggregateOutputType | null
    _min: JobCardMinAggregateOutputType | null
    _max: JobCardMaxAggregateOutputType | null
  }

  type GetJobCardGroupByPayload<T extends JobCardGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<JobCardGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof JobCardGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], JobCardGroupByOutputType[P]>
            : GetScalarType<T[P], JobCardGroupByOutputType[P]>
        }
      >
    >


  export type JobCardSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    billNo?: boolean
    date?: boolean
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName?: boolean
    address?: boolean
    mobileNumber?: boolean
    complaint?: boolean
    model?: boolean
    admissionFees?: boolean
    aadhaarNumber?: boolean
    estimate?: boolean
    advance?: boolean
    finalAmount?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    customerId?: boolean
    createdBy?: boolean | UserDefaultArgs<ExtArgs>
    customer?: boolean | JobCard$customerArgs<ExtArgs>
  }, ExtArgs["result"]["jobCard"]>



  export type JobCardSelectScalar = {
    id?: boolean
    billNo?: boolean
    date?: boolean
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName?: boolean
    address?: boolean
    mobileNumber?: boolean
    complaint?: boolean
    model?: boolean
    admissionFees?: boolean
    aadhaarNumber?: boolean
    estimate?: boolean
    advance?: boolean
    finalAmount?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    customerId?: boolean
  }

  export type JobCardOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "billNo" | "date" | "isOn" | "isOff" | "hasBattery" | "hasDoor" | "hasSim" | "hasSlot" | "customerName" | "address" | "mobileNumber" | "complaint" | "model" | "admissionFees" | "aadhaarNumber" | "estimate" | "advance" | "finalAmount" | "status" | "createdAt" | "updatedAt" | "userId" | "customerId", ExtArgs["result"]["jobCard"]>
  export type JobCardInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    createdBy?: boolean | UserDefaultArgs<ExtArgs>
    customer?: boolean | JobCard$customerArgs<ExtArgs>
  }

  export type $JobCardPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "JobCard"
    objects: {
      createdBy: Prisma.$UserPayload<ExtArgs>
      customer: Prisma.$CustomerPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      billNo: number | null
      date: Date
      isOn: boolean
      isOff: boolean
      hasBattery: boolean
      hasDoor: boolean
      hasSim: boolean
      hasSlot: boolean
      customerName: string
      address: string | null
      mobileNumber: string
      complaint: string
      model: string
      admissionFees: number | null
      aadhaarNumber: string | null
      estimate: number | null
      advance: number | null
      finalAmount: number | null
      status: string
      createdAt: Date
      updatedAt: Date
      userId: number
      customerId: number | null
    }, ExtArgs["result"]["jobCard"]>
    composites: {}
  }

  type JobCardGetPayload<S extends boolean | null | undefined | JobCardDefaultArgs> = $Result.GetResult<Prisma.$JobCardPayload, S>

  type JobCardCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<JobCardFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: JobCardCountAggregateInputType | true
    }

  export interface JobCardDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['JobCard'], meta: { name: 'JobCard' } }
    /**
     * Find zero or one JobCard that matches the filter.
     * @param {JobCardFindUniqueArgs} args - Arguments to find a JobCard
     * @example
     * // Get one JobCard
     * const jobCard = await prisma.jobCard.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends JobCardFindUniqueArgs>(args: SelectSubset<T, JobCardFindUniqueArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one JobCard that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {JobCardFindUniqueOrThrowArgs} args - Arguments to find a JobCard
     * @example
     * // Get one JobCard
     * const jobCard = await prisma.jobCard.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends JobCardFindUniqueOrThrowArgs>(args: SelectSubset<T, JobCardFindUniqueOrThrowArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first JobCard that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {JobCardFindFirstArgs} args - Arguments to find a JobCard
     * @example
     * // Get one JobCard
     * const jobCard = await prisma.jobCard.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends JobCardFindFirstArgs>(args?: SelectSubset<T, JobCardFindFirstArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first JobCard that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {JobCardFindFirstOrThrowArgs} args - Arguments to find a JobCard
     * @example
     * // Get one JobCard
     * const jobCard = await prisma.jobCard.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends JobCardFindFirstOrThrowArgs>(args?: SelectSubset<T, JobCardFindFirstOrThrowArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more JobCards that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {JobCardFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all JobCards
     * const jobCards = await prisma.jobCard.findMany()
     * 
     * // Get first 10 JobCards
     * const jobCards = await prisma.jobCard.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const jobCardWithIdOnly = await prisma.jobCard.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends JobCardFindManyArgs>(args?: SelectSubset<T, JobCardFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a JobCard.
     * @param {JobCardCreateArgs} args - Arguments to create a JobCard.
     * @example
     * // Create one JobCard
     * const JobCard = await prisma.jobCard.create({
     *   data: {
     *     // ... data to create a JobCard
     *   }
     * })
     * 
     */
    create<T extends JobCardCreateArgs>(args: SelectSubset<T, JobCardCreateArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many JobCards.
     * @param {JobCardCreateManyArgs} args - Arguments to create many JobCards.
     * @example
     * // Create many JobCards
     * const jobCard = await prisma.jobCard.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends JobCardCreateManyArgs>(args?: SelectSubset<T, JobCardCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Delete a JobCard.
     * @param {JobCardDeleteArgs} args - Arguments to delete one JobCard.
     * @example
     * // Delete one JobCard
     * const JobCard = await prisma.jobCard.delete({
     *   where: {
     *     // ... filter to delete one JobCard
     *   }
     * })
     * 
     */
    delete<T extends JobCardDeleteArgs>(args: SelectSubset<T, JobCardDeleteArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one JobCard.
     * @param {JobCardUpdateArgs} args - Arguments to update one JobCard.
     * @example
     * // Update one JobCard
     * const jobCard = await prisma.jobCard.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends JobCardUpdateArgs>(args: SelectSubset<T, JobCardUpdateArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more JobCards.
     * @param {JobCardDeleteManyArgs} args - Arguments to filter JobCards to delete.
     * @example
     * // Delete a few JobCards
     * const { count } = await prisma.jobCard.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends JobCardDeleteManyArgs>(args?: SelectSubset<T, JobCardDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more JobCards.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {JobCardUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many JobCards
     * const jobCard = await prisma.jobCard.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends JobCardUpdateManyArgs>(args: SelectSubset<T, JobCardUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one JobCard.
     * @param {JobCardUpsertArgs} args - Arguments to update or create a JobCard.
     * @example
     * // Update or create a JobCard
     * const jobCard = await prisma.jobCard.upsert({
     *   create: {
     *     // ... data to create a JobCard
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the JobCard we want to update
     *   }
     * })
     */
    upsert<T extends JobCardUpsertArgs>(args: SelectSubset<T, JobCardUpsertArgs<ExtArgs>>): Prisma__JobCardClient<$Result.GetResult<Prisma.$JobCardPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of JobCards.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {JobCardCountArgs} args - Arguments to filter JobCards to count.
     * @example
     * // Count the number of JobCards
     * const count = await prisma.jobCard.count({
     *   where: {
     *     // ... the filter for the JobCards we want to count
     *   }
     * })
    **/
    count<T extends JobCardCountArgs>(
      args?: Subset<T, JobCardCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], JobCardCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a JobCard.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {JobCardAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends JobCardAggregateArgs>(args: Subset<T, JobCardAggregateArgs>): Prisma.PrismaPromise<GetJobCardAggregateType<T>>

    /**
     * Group by JobCard.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {JobCardGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends JobCardGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: JobCardGroupByArgs['orderBy'] }
        : { orderBy?: JobCardGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, JobCardGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetJobCardGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the JobCard model
   */
  readonly fields: JobCardFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for JobCard.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__JobCardClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    createdBy<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    customer<T extends JobCard$customerArgs<ExtArgs> = {}>(args?: Subset<T, JobCard$customerArgs<ExtArgs>>): Prisma__CustomerClient<$Result.GetResult<Prisma.$CustomerPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the JobCard model
   */
  interface JobCardFieldRefs {
    readonly id: FieldRef<"JobCard", 'Int'>
    readonly billNo: FieldRef<"JobCard", 'Int'>
    readonly date: FieldRef<"JobCard", 'DateTime'>
    readonly isOn: FieldRef<"JobCard", 'Boolean'>
    readonly isOff: FieldRef<"JobCard", 'Boolean'>
    readonly hasBattery: FieldRef<"JobCard", 'Boolean'>
    readonly hasDoor: FieldRef<"JobCard", 'Boolean'>
    readonly hasSim: FieldRef<"JobCard", 'Boolean'>
    readonly hasSlot: FieldRef<"JobCard", 'Boolean'>
    readonly customerName: FieldRef<"JobCard", 'String'>
    readonly address: FieldRef<"JobCard", 'String'>
    readonly mobileNumber: FieldRef<"JobCard", 'String'>
    readonly complaint: FieldRef<"JobCard", 'String'>
    readonly model: FieldRef<"JobCard", 'String'>
    readonly admissionFees: FieldRef<"JobCard", 'Float'>
    readonly aadhaarNumber: FieldRef<"JobCard", 'String'>
    readonly estimate: FieldRef<"JobCard", 'Float'>
    readonly advance: FieldRef<"JobCard", 'Float'>
    readonly finalAmount: FieldRef<"JobCard", 'Float'>
    readonly status: FieldRef<"JobCard", 'String'>
    readonly createdAt: FieldRef<"JobCard", 'DateTime'>
    readonly updatedAt: FieldRef<"JobCard", 'DateTime'>
    readonly userId: FieldRef<"JobCard", 'Int'>
    readonly customerId: FieldRef<"JobCard", 'Int'>
  }
    

  // Custom InputTypes
  /**
   * JobCard findUnique
   */
  export type JobCardFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * Filter, which JobCard to fetch.
     */
    where: JobCardWhereUniqueInput
  }

  /**
   * JobCard findUniqueOrThrow
   */
  export type JobCardFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * Filter, which JobCard to fetch.
     */
    where: JobCardWhereUniqueInput
  }

  /**
   * JobCard findFirst
   */
  export type JobCardFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * Filter, which JobCard to fetch.
     */
    where?: JobCardWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of JobCards to fetch.
     */
    orderBy?: JobCardOrderByWithRelationInput | JobCardOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for JobCards.
     */
    cursor?: JobCardWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` JobCards from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` JobCards.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of JobCards.
     */
    distinct?: JobCardScalarFieldEnum | JobCardScalarFieldEnum[]
  }

  /**
   * JobCard findFirstOrThrow
   */
  export type JobCardFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * Filter, which JobCard to fetch.
     */
    where?: JobCardWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of JobCards to fetch.
     */
    orderBy?: JobCardOrderByWithRelationInput | JobCardOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for JobCards.
     */
    cursor?: JobCardWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` JobCards from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` JobCards.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of JobCards.
     */
    distinct?: JobCardScalarFieldEnum | JobCardScalarFieldEnum[]
  }

  /**
   * JobCard findMany
   */
  export type JobCardFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * Filter, which JobCards to fetch.
     */
    where?: JobCardWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of JobCards to fetch.
     */
    orderBy?: JobCardOrderByWithRelationInput | JobCardOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing JobCards.
     */
    cursor?: JobCardWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` JobCards from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` JobCards.
     */
    skip?: number
    distinct?: JobCardScalarFieldEnum | JobCardScalarFieldEnum[]
  }

  /**
   * JobCard create
   */
  export type JobCardCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * The data needed to create a JobCard.
     */
    data: XOR<JobCardCreateInput, JobCardUncheckedCreateInput>
  }

  /**
   * JobCard createMany
   */
  export type JobCardCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many JobCards.
     */
    data: JobCardCreateManyInput | JobCardCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * JobCard update
   */
  export type JobCardUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * The data needed to update a JobCard.
     */
    data: XOR<JobCardUpdateInput, JobCardUncheckedUpdateInput>
    /**
     * Choose, which JobCard to update.
     */
    where: JobCardWhereUniqueInput
  }

  /**
   * JobCard updateMany
   */
  export type JobCardUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update JobCards.
     */
    data: XOR<JobCardUpdateManyMutationInput, JobCardUncheckedUpdateManyInput>
    /**
     * Filter which JobCards to update
     */
    where?: JobCardWhereInput
    /**
     * Limit how many JobCards to update.
     */
    limit?: number
  }

  /**
   * JobCard upsert
   */
  export type JobCardUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * The filter to search for the JobCard to update in case it exists.
     */
    where: JobCardWhereUniqueInput
    /**
     * In case the JobCard found by the `where` argument doesn't exist, create a new JobCard with this data.
     */
    create: XOR<JobCardCreateInput, JobCardUncheckedCreateInput>
    /**
     * In case the JobCard was found with the provided `where` argument, update it with this data.
     */
    update: XOR<JobCardUpdateInput, JobCardUncheckedUpdateInput>
  }

  /**
   * JobCard delete
   */
  export type JobCardDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
    /**
     * Filter which JobCard to delete.
     */
    where: JobCardWhereUniqueInput
  }

  /**
   * JobCard deleteMany
   */
  export type JobCardDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which JobCards to delete
     */
    where?: JobCardWhereInput
    /**
     * Limit how many JobCards to delete.
     */
    limit?: number
  }

  /**
   * JobCard.customer
   */
  export type JobCard$customerArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Customer
     */
    select?: CustomerSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Customer
     */
    omit?: CustomerOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomerInclude<ExtArgs> | null
    where?: CustomerWhereInput
  }

  /**
   * JobCard without action
   */
  export type JobCardDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the JobCard
     */
    select?: JobCardSelect<ExtArgs> | null
    /**
     * Omit specific fields from the JobCard
     */
    omit?: JobCardOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: JobCardInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    username: 'username',
    password: 'password',
    name: 'name',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const CustomerScalarFieldEnum: {
    id: 'id',
    name: 'name',
    mobileNumber: 'mobileNumber',
    address: 'address',
    aadhaarNumber: 'aadhaarNumber',
    visitCount: 'visitCount',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type CustomerScalarFieldEnum = (typeof CustomerScalarFieldEnum)[keyof typeof CustomerScalarFieldEnum]


  export const JobCardScalarFieldEnum: {
    id: 'id',
    billNo: 'billNo',
    date: 'date',
    isOn: 'isOn',
    isOff: 'isOff',
    hasBattery: 'hasBattery',
    hasDoor: 'hasDoor',
    hasSim: 'hasSim',
    hasSlot: 'hasSlot',
    customerName: 'customerName',
    address: 'address',
    mobileNumber: 'mobileNumber',
    complaint: 'complaint',
    model: 'model',
    admissionFees: 'admissionFees',
    aadhaarNumber: 'aadhaarNumber',
    estimate: 'estimate',
    advance: 'advance',
    finalAmount: 'finalAmount',
    status: 'status',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    userId: 'userId',
    customerId: 'customerId'
  };

  export type JobCardScalarFieldEnum = (typeof JobCardScalarFieldEnum)[keyof typeof JobCardScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const UserOrderByRelevanceFieldEnum: {
    username: 'username',
    password: 'password',
    name: 'name'
  };

  export type UserOrderByRelevanceFieldEnum = (typeof UserOrderByRelevanceFieldEnum)[keyof typeof UserOrderByRelevanceFieldEnum]


  export const CustomerOrderByRelevanceFieldEnum: {
    name: 'name',
    mobileNumber: 'mobileNumber',
    address: 'address',
    aadhaarNumber: 'aadhaarNumber'
  };

  export type CustomerOrderByRelevanceFieldEnum = (typeof CustomerOrderByRelevanceFieldEnum)[keyof typeof CustomerOrderByRelevanceFieldEnum]


  export const JobCardOrderByRelevanceFieldEnum: {
    customerName: 'customerName',
    address: 'address',
    mobileNumber: 'mobileNumber',
    complaint: 'complaint',
    model: 'model',
    aadhaarNumber: 'aadhaarNumber',
    status: 'status'
  };

  export type JobCardOrderByRelevanceFieldEnum = (typeof JobCardOrderByRelevanceFieldEnum)[keyof typeof JobCardOrderByRelevanceFieldEnum]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: IntFilter<"User"> | number
    username?: StringFilter<"User"> | string
    password?: StringFilter<"User"> | string
    name?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    jobCards?: JobCardListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    name?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    jobCards?: JobCardOrderByRelationAggregateInput
    _relevance?: UserOrderByRelevanceInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    username?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    password?: StringFilter<"User"> | string
    name?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    jobCards?: JobCardListRelationFilter
  }, "id" | "username">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    name?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _avg?: UserAvgOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
    _sum?: UserSumOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"User"> | number
    username?: StringWithAggregatesFilter<"User"> | string
    password?: StringWithAggregatesFilter<"User"> | string
    name?: StringNullableWithAggregatesFilter<"User"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type CustomerWhereInput = {
    AND?: CustomerWhereInput | CustomerWhereInput[]
    OR?: CustomerWhereInput[]
    NOT?: CustomerWhereInput | CustomerWhereInput[]
    id?: IntFilter<"Customer"> | number
    name?: StringFilter<"Customer"> | string
    mobileNumber?: StringFilter<"Customer"> | string
    address?: StringNullableFilter<"Customer"> | string | null
    aadhaarNumber?: StringNullableFilter<"Customer"> | string | null
    visitCount?: IntFilter<"Customer"> | number
    createdAt?: DateTimeFilter<"Customer"> | Date | string
    updatedAt?: DateTimeFilter<"Customer"> | Date | string
    jobCards?: JobCardListRelationFilter
  }

  export type CustomerOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    mobileNumber?: SortOrder
    address?: SortOrderInput | SortOrder
    aadhaarNumber?: SortOrderInput | SortOrder
    visitCount?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    jobCards?: JobCardOrderByRelationAggregateInput
    _relevance?: CustomerOrderByRelevanceInput
  }

  export type CustomerWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    mobileNumber?: string
    AND?: CustomerWhereInput | CustomerWhereInput[]
    OR?: CustomerWhereInput[]
    NOT?: CustomerWhereInput | CustomerWhereInput[]
    name?: StringFilter<"Customer"> | string
    address?: StringNullableFilter<"Customer"> | string | null
    aadhaarNumber?: StringNullableFilter<"Customer"> | string | null
    visitCount?: IntFilter<"Customer"> | number
    createdAt?: DateTimeFilter<"Customer"> | Date | string
    updatedAt?: DateTimeFilter<"Customer"> | Date | string
    jobCards?: JobCardListRelationFilter
  }, "id" | "mobileNumber">

  export type CustomerOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    mobileNumber?: SortOrder
    address?: SortOrderInput | SortOrder
    aadhaarNumber?: SortOrderInput | SortOrder
    visitCount?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: CustomerCountOrderByAggregateInput
    _avg?: CustomerAvgOrderByAggregateInput
    _max?: CustomerMaxOrderByAggregateInput
    _min?: CustomerMinOrderByAggregateInput
    _sum?: CustomerSumOrderByAggregateInput
  }

  export type CustomerScalarWhereWithAggregatesInput = {
    AND?: CustomerScalarWhereWithAggregatesInput | CustomerScalarWhereWithAggregatesInput[]
    OR?: CustomerScalarWhereWithAggregatesInput[]
    NOT?: CustomerScalarWhereWithAggregatesInput | CustomerScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Customer"> | number
    name?: StringWithAggregatesFilter<"Customer"> | string
    mobileNumber?: StringWithAggregatesFilter<"Customer"> | string
    address?: StringNullableWithAggregatesFilter<"Customer"> | string | null
    aadhaarNumber?: StringNullableWithAggregatesFilter<"Customer"> | string | null
    visitCount?: IntWithAggregatesFilter<"Customer"> | number
    createdAt?: DateTimeWithAggregatesFilter<"Customer"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Customer"> | Date | string
  }

  export type JobCardWhereInput = {
    AND?: JobCardWhereInput | JobCardWhereInput[]
    OR?: JobCardWhereInput[]
    NOT?: JobCardWhereInput | JobCardWhereInput[]
    id?: IntFilter<"JobCard"> | number
    billNo?: IntNullableFilter<"JobCard"> | number | null
    date?: DateTimeFilter<"JobCard"> | Date | string
    isOn?: BoolFilter<"JobCard"> | boolean
    isOff?: BoolFilter<"JobCard"> | boolean
    hasBattery?: BoolFilter<"JobCard"> | boolean
    hasDoor?: BoolFilter<"JobCard"> | boolean
    hasSim?: BoolFilter<"JobCard"> | boolean
    hasSlot?: BoolFilter<"JobCard"> | boolean
    customerName?: StringFilter<"JobCard"> | string
    address?: StringNullableFilter<"JobCard"> | string | null
    mobileNumber?: StringFilter<"JobCard"> | string
    complaint?: StringFilter<"JobCard"> | string
    model?: StringFilter<"JobCard"> | string
    admissionFees?: FloatNullableFilter<"JobCard"> | number | null
    aadhaarNumber?: StringNullableFilter<"JobCard"> | string | null
    estimate?: FloatNullableFilter<"JobCard"> | number | null
    advance?: FloatNullableFilter<"JobCard"> | number | null
    finalAmount?: FloatNullableFilter<"JobCard"> | number | null
    status?: StringFilter<"JobCard"> | string
    createdAt?: DateTimeFilter<"JobCard"> | Date | string
    updatedAt?: DateTimeFilter<"JobCard"> | Date | string
    userId?: IntFilter<"JobCard"> | number
    customerId?: IntNullableFilter<"JobCard"> | number | null
    createdBy?: XOR<UserScalarRelationFilter, UserWhereInput>
    customer?: XOR<CustomerNullableScalarRelationFilter, CustomerWhereInput> | null
  }

  export type JobCardOrderByWithRelationInput = {
    id?: SortOrder
    billNo?: SortOrderInput | SortOrder
    date?: SortOrder
    isOn?: SortOrder
    isOff?: SortOrder
    hasBattery?: SortOrder
    hasDoor?: SortOrder
    hasSim?: SortOrder
    hasSlot?: SortOrder
    customerName?: SortOrder
    address?: SortOrderInput | SortOrder
    mobileNumber?: SortOrder
    complaint?: SortOrder
    model?: SortOrder
    admissionFees?: SortOrderInput | SortOrder
    aadhaarNumber?: SortOrderInput | SortOrder
    estimate?: SortOrderInput | SortOrder
    advance?: SortOrderInput | SortOrder
    finalAmount?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    customerId?: SortOrderInput | SortOrder
    createdBy?: UserOrderByWithRelationInput
    customer?: CustomerOrderByWithRelationInput
    _relevance?: JobCardOrderByRelevanceInput
  }

  export type JobCardWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    billNo?: number
    AND?: JobCardWhereInput | JobCardWhereInput[]
    OR?: JobCardWhereInput[]
    NOT?: JobCardWhereInput | JobCardWhereInput[]
    date?: DateTimeFilter<"JobCard"> | Date | string
    isOn?: BoolFilter<"JobCard"> | boolean
    isOff?: BoolFilter<"JobCard"> | boolean
    hasBattery?: BoolFilter<"JobCard"> | boolean
    hasDoor?: BoolFilter<"JobCard"> | boolean
    hasSim?: BoolFilter<"JobCard"> | boolean
    hasSlot?: BoolFilter<"JobCard"> | boolean
    customerName?: StringFilter<"JobCard"> | string
    address?: StringNullableFilter<"JobCard"> | string | null
    mobileNumber?: StringFilter<"JobCard"> | string
    complaint?: StringFilter<"JobCard"> | string
    model?: StringFilter<"JobCard"> | string
    admissionFees?: FloatNullableFilter<"JobCard"> | number | null
    aadhaarNumber?: StringNullableFilter<"JobCard"> | string | null
    estimate?: FloatNullableFilter<"JobCard"> | number | null
    advance?: FloatNullableFilter<"JobCard"> | number | null
    finalAmount?: FloatNullableFilter<"JobCard"> | number | null
    status?: StringFilter<"JobCard"> | string
    createdAt?: DateTimeFilter<"JobCard"> | Date | string
    updatedAt?: DateTimeFilter<"JobCard"> | Date | string
    userId?: IntFilter<"JobCard"> | number
    customerId?: IntNullableFilter<"JobCard"> | number | null
    createdBy?: XOR<UserScalarRelationFilter, UserWhereInput>
    customer?: XOR<CustomerNullableScalarRelationFilter, CustomerWhereInput> | null
  }, "id" | "billNo">

  export type JobCardOrderByWithAggregationInput = {
    id?: SortOrder
    billNo?: SortOrderInput | SortOrder
    date?: SortOrder
    isOn?: SortOrder
    isOff?: SortOrder
    hasBattery?: SortOrder
    hasDoor?: SortOrder
    hasSim?: SortOrder
    hasSlot?: SortOrder
    customerName?: SortOrder
    address?: SortOrderInput | SortOrder
    mobileNumber?: SortOrder
    complaint?: SortOrder
    model?: SortOrder
    admissionFees?: SortOrderInput | SortOrder
    aadhaarNumber?: SortOrderInput | SortOrder
    estimate?: SortOrderInput | SortOrder
    advance?: SortOrderInput | SortOrder
    finalAmount?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    customerId?: SortOrderInput | SortOrder
    _count?: JobCardCountOrderByAggregateInput
    _avg?: JobCardAvgOrderByAggregateInput
    _max?: JobCardMaxOrderByAggregateInput
    _min?: JobCardMinOrderByAggregateInput
    _sum?: JobCardSumOrderByAggregateInput
  }

  export type JobCardScalarWhereWithAggregatesInput = {
    AND?: JobCardScalarWhereWithAggregatesInput | JobCardScalarWhereWithAggregatesInput[]
    OR?: JobCardScalarWhereWithAggregatesInput[]
    NOT?: JobCardScalarWhereWithAggregatesInput | JobCardScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"JobCard"> | number
    billNo?: IntNullableWithAggregatesFilter<"JobCard"> | number | null
    date?: DateTimeWithAggregatesFilter<"JobCard"> | Date | string
    isOn?: BoolWithAggregatesFilter<"JobCard"> | boolean
    isOff?: BoolWithAggregatesFilter<"JobCard"> | boolean
    hasBattery?: BoolWithAggregatesFilter<"JobCard"> | boolean
    hasDoor?: BoolWithAggregatesFilter<"JobCard"> | boolean
    hasSim?: BoolWithAggregatesFilter<"JobCard"> | boolean
    hasSlot?: BoolWithAggregatesFilter<"JobCard"> | boolean
    customerName?: StringWithAggregatesFilter<"JobCard"> | string
    address?: StringNullableWithAggregatesFilter<"JobCard"> | string | null
    mobileNumber?: StringWithAggregatesFilter<"JobCard"> | string
    complaint?: StringWithAggregatesFilter<"JobCard"> | string
    model?: StringWithAggregatesFilter<"JobCard"> | string
    admissionFees?: FloatNullableWithAggregatesFilter<"JobCard"> | number | null
    aadhaarNumber?: StringNullableWithAggregatesFilter<"JobCard"> | string | null
    estimate?: FloatNullableWithAggregatesFilter<"JobCard"> | number | null
    advance?: FloatNullableWithAggregatesFilter<"JobCard"> | number | null
    finalAmount?: FloatNullableWithAggregatesFilter<"JobCard"> | number | null
    status?: StringWithAggregatesFilter<"JobCard"> | string
    createdAt?: DateTimeWithAggregatesFilter<"JobCard"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"JobCard"> | Date | string
    userId?: IntWithAggregatesFilter<"JobCard"> | number
    customerId?: IntNullableWithAggregatesFilter<"JobCard"> | number | null
  }

  export type UserCreateInput = {
    username: string
    password: string
    name?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    jobCards?: JobCardCreateNestedManyWithoutCreatedByInput
  }

  export type UserUncheckedCreateInput = {
    id?: number
    username: string
    password: string
    name?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    jobCards?: JobCardUncheckedCreateNestedManyWithoutCreatedByInput
  }

  export type UserUpdateInput = {
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    jobCards?: JobCardUpdateManyWithoutCreatedByNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    jobCards?: JobCardUncheckedUpdateManyWithoutCreatedByNestedInput
  }

  export type UserCreateManyInput = {
    id?: number
    username: string
    password: string
    name?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CustomerCreateInput = {
    name: string
    mobileNumber: string
    address?: string | null
    aadhaarNumber?: string | null
    visitCount?: number
    createdAt?: Date | string
    updatedAt?: Date | string
    jobCards?: JobCardCreateNestedManyWithoutCustomerInput
  }

  export type CustomerUncheckedCreateInput = {
    id?: number
    name: string
    mobileNumber: string
    address?: string | null
    aadhaarNumber?: string | null
    visitCount?: number
    createdAt?: Date | string
    updatedAt?: Date | string
    jobCards?: JobCardUncheckedCreateNestedManyWithoutCustomerInput
  }

  export type CustomerUpdateInput = {
    name?: StringFieldUpdateOperationsInput | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    visitCount?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    jobCards?: JobCardUpdateManyWithoutCustomerNestedInput
  }

  export type CustomerUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    visitCount?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    jobCards?: JobCardUncheckedUpdateManyWithoutCustomerNestedInput
  }

  export type CustomerCreateManyInput = {
    id?: number
    name: string
    mobileNumber: string
    address?: string | null
    aadhaarNumber?: string | null
    visitCount?: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type CustomerUpdateManyMutationInput = {
    name?: StringFieldUpdateOperationsInput | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    visitCount?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CustomerUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    visitCount?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type JobCardCreateInput = {
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    createdBy: UserCreateNestedOneWithoutJobCardsInput
    customer?: CustomerCreateNestedOneWithoutJobCardsInput
  }

  export type JobCardUncheckedCreateInput = {
    id?: number
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    userId: number
    customerId?: number | null
  }

  export type JobCardUpdateInput = {
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdBy?: UserUpdateOneRequiredWithoutJobCardsNestedInput
    customer?: CustomerUpdateOneWithoutJobCardsNestedInput
  }

  export type JobCardUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: IntFieldUpdateOperationsInput | number
    customerId?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type JobCardCreateManyInput = {
    id?: number
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    userId: number
    customerId?: number | null
  }

  export type JobCardUpdateManyMutationInput = {
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type JobCardUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: IntFieldUpdateOperationsInput | number
    customerId?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type JobCardListRelationFilter = {
    every?: JobCardWhereInput
    some?: JobCardWhereInput
    none?: JobCardWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type JobCardOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserOrderByRelevanceInput = {
    fields: UserOrderByRelevanceFieldEnum | UserOrderByRelevanceFieldEnum[]
    sort: SortOrder
    search: string
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    name?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    name?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    name?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type CustomerOrderByRelevanceInput = {
    fields: CustomerOrderByRelevanceFieldEnum | CustomerOrderByRelevanceFieldEnum[]
    sort: SortOrder
    search: string
  }

  export type CustomerCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    mobileNumber?: SortOrder
    address?: SortOrder
    aadhaarNumber?: SortOrder
    visitCount?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type CustomerAvgOrderByAggregateInput = {
    id?: SortOrder
    visitCount?: SortOrder
  }

  export type CustomerMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    mobileNumber?: SortOrder
    address?: SortOrder
    aadhaarNumber?: SortOrder
    visitCount?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type CustomerMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    mobileNumber?: SortOrder
    address?: SortOrder
    aadhaarNumber?: SortOrder
    visitCount?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type CustomerSumOrderByAggregateInput = {
    id?: SortOrder
    visitCount?: SortOrder
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type FloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type CustomerNullableScalarRelationFilter = {
    is?: CustomerWhereInput | null
    isNot?: CustomerWhereInput | null
  }

  export type JobCardOrderByRelevanceInput = {
    fields: JobCardOrderByRelevanceFieldEnum | JobCardOrderByRelevanceFieldEnum[]
    sort: SortOrder
    search: string
  }

  export type JobCardCountOrderByAggregateInput = {
    id?: SortOrder
    billNo?: SortOrder
    date?: SortOrder
    isOn?: SortOrder
    isOff?: SortOrder
    hasBattery?: SortOrder
    hasDoor?: SortOrder
    hasSim?: SortOrder
    hasSlot?: SortOrder
    customerName?: SortOrder
    address?: SortOrder
    mobileNumber?: SortOrder
    complaint?: SortOrder
    model?: SortOrder
    admissionFees?: SortOrder
    aadhaarNumber?: SortOrder
    estimate?: SortOrder
    advance?: SortOrder
    finalAmount?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
  }

  export type JobCardAvgOrderByAggregateInput = {
    id?: SortOrder
    billNo?: SortOrder
    admissionFees?: SortOrder
    estimate?: SortOrder
    advance?: SortOrder
    finalAmount?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
  }

  export type JobCardMaxOrderByAggregateInput = {
    id?: SortOrder
    billNo?: SortOrder
    date?: SortOrder
    isOn?: SortOrder
    isOff?: SortOrder
    hasBattery?: SortOrder
    hasDoor?: SortOrder
    hasSim?: SortOrder
    hasSlot?: SortOrder
    customerName?: SortOrder
    address?: SortOrder
    mobileNumber?: SortOrder
    complaint?: SortOrder
    model?: SortOrder
    admissionFees?: SortOrder
    aadhaarNumber?: SortOrder
    estimate?: SortOrder
    advance?: SortOrder
    finalAmount?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
  }

  export type JobCardMinOrderByAggregateInput = {
    id?: SortOrder
    billNo?: SortOrder
    date?: SortOrder
    isOn?: SortOrder
    isOff?: SortOrder
    hasBattery?: SortOrder
    hasDoor?: SortOrder
    hasSim?: SortOrder
    hasSlot?: SortOrder
    customerName?: SortOrder
    address?: SortOrder
    mobileNumber?: SortOrder
    complaint?: SortOrder
    model?: SortOrder
    admissionFees?: SortOrder
    aadhaarNumber?: SortOrder
    estimate?: SortOrder
    advance?: SortOrder
    finalAmount?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
  }

  export type JobCardSumOrderByAggregateInput = {
    id?: SortOrder
    billNo?: SortOrder
    admissionFees?: SortOrder
    estimate?: SortOrder
    advance?: SortOrder
    finalAmount?: SortOrder
    userId?: SortOrder
    customerId?: SortOrder
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type JobCardCreateNestedManyWithoutCreatedByInput = {
    create?: XOR<JobCardCreateWithoutCreatedByInput, JobCardUncheckedCreateWithoutCreatedByInput> | JobCardCreateWithoutCreatedByInput[] | JobCardUncheckedCreateWithoutCreatedByInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCreatedByInput | JobCardCreateOrConnectWithoutCreatedByInput[]
    createMany?: JobCardCreateManyCreatedByInputEnvelope
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
  }

  export type JobCardUncheckedCreateNestedManyWithoutCreatedByInput = {
    create?: XOR<JobCardCreateWithoutCreatedByInput, JobCardUncheckedCreateWithoutCreatedByInput> | JobCardCreateWithoutCreatedByInput[] | JobCardUncheckedCreateWithoutCreatedByInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCreatedByInput | JobCardCreateOrConnectWithoutCreatedByInput[]
    createMany?: JobCardCreateManyCreatedByInputEnvelope
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type JobCardUpdateManyWithoutCreatedByNestedInput = {
    create?: XOR<JobCardCreateWithoutCreatedByInput, JobCardUncheckedCreateWithoutCreatedByInput> | JobCardCreateWithoutCreatedByInput[] | JobCardUncheckedCreateWithoutCreatedByInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCreatedByInput | JobCardCreateOrConnectWithoutCreatedByInput[]
    upsert?: JobCardUpsertWithWhereUniqueWithoutCreatedByInput | JobCardUpsertWithWhereUniqueWithoutCreatedByInput[]
    createMany?: JobCardCreateManyCreatedByInputEnvelope
    set?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    disconnect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    delete?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    update?: JobCardUpdateWithWhereUniqueWithoutCreatedByInput | JobCardUpdateWithWhereUniqueWithoutCreatedByInput[]
    updateMany?: JobCardUpdateManyWithWhereWithoutCreatedByInput | JobCardUpdateManyWithWhereWithoutCreatedByInput[]
    deleteMany?: JobCardScalarWhereInput | JobCardScalarWhereInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type JobCardUncheckedUpdateManyWithoutCreatedByNestedInput = {
    create?: XOR<JobCardCreateWithoutCreatedByInput, JobCardUncheckedCreateWithoutCreatedByInput> | JobCardCreateWithoutCreatedByInput[] | JobCardUncheckedCreateWithoutCreatedByInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCreatedByInput | JobCardCreateOrConnectWithoutCreatedByInput[]
    upsert?: JobCardUpsertWithWhereUniqueWithoutCreatedByInput | JobCardUpsertWithWhereUniqueWithoutCreatedByInput[]
    createMany?: JobCardCreateManyCreatedByInputEnvelope
    set?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    disconnect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    delete?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    update?: JobCardUpdateWithWhereUniqueWithoutCreatedByInput | JobCardUpdateWithWhereUniqueWithoutCreatedByInput[]
    updateMany?: JobCardUpdateManyWithWhereWithoutCreatedByInput | JobCardUpdateManyWithWhereWithoutCreatedByInput[]
    deleteMany?: JobCardScalarWhereInput | JobCardScalarWhereInput[]
  }

  export type JobCardCreateNestedManyWithoutCustomerInput = {
    create?: XOR<JobCardCreateWithoutCustomerInput, JobCardUncheckedCreateWithoutCustomerInput> | JobCardCreateWithoutCustomerInput[] | JobCardUncheckedCreateWithoutCustomerInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCustomerInput | JobCardCreateOrConnectWithoutCustomerInput[]
    createMany?: JobCardCreateManyCustomerInputEnvelope
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
  }

  export type JobCardUncheckedCreateNestedManyWithoutCustomerInput = {
    create?: XOR<JobCardCreateWithoutCustomerInput, JobCardUncheckedCreateWithoutCustomerInput> | JobCardCreateWithoutCustomerInput[] | JobCardUncheckedCreateWithoutCustomerInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCustomerInput | JobCardCreateOrConnectWithoutCustomerInput[]
    createMany?: JobCardCreateManyCustomerInputEnvelope
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
  }

  export type JobCardUpdateManyWithoutCustomerNestedInput = {
    create?: XOR<JobCardCreateWithoutCustomerInput, JobCardUncheckedCreateWithoutCustomerInput> | JobCardCreateWithoutCustomerInput[] | JobCardUncheckedCreateWithoutCustomerInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCustomerInput | JobCardCreateOrConnectWithoutCustomerInput[]
    upsert?: JobCardUpsertWithWhereUniqueWithoutCustomerInput | JobCardUpsertWithWhereUniqueWithoutCustomerInput[]
    createMany?: JobCardCreateManyCustomerInputEnvelope
    set?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    disconnect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    delete?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    update?: JobCardUpdateWithWhereUniqueWithoutCustomerInput | JobCardUpdateWithWhereUniqueWithoutCustomerInput[]
    updateMany?: JobCardUpdateManyWithWhereWithoutCustomerInput | JobCardUpdateManyWithWhereWithoutCustomerInput[]
    deleteMany?: JobCardScalarWhereInput | JobCardScalarWhereInput[]
  }

  export type JobCardUncheckedUpdateManyWithoutCustomerNestedInput = {
    create?: XOR<JobCardCreateWithoutCustomerInput, JobCardUncheckedCreateWithoutCustomerInput> | JobCardCreateWithoutCustomerInput[] | JobCardUncheckedCreateWithoutCustomerInput[]
    connectOrCreate?: JobCardCreateOrConnectWithoutCustomerInput | JobCardCreateOrConnectWithoutCustomerInput[]
    upsert?: JobCardUpsertWithWhereUniqueWithoutCustomerInput | JobCardUpsertWithWhereUniqueWithoutCustomerInput[]
    createMany?: JobCardCreateManyCustomerInputEnvelope
    set?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    disconnect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    delete?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    connect?: JobCardWhereUniqueInput | JobCardWhereUniqueInput[]
    update?: JobCardUpdateWithWhereUniqueWithoutCustomerInput | JobCardUpdateWithWhereUniqueWithoutCustomerInput[]
    updateMany?: JobCardUpdateManyWithWhereWithoutCustomerInput | JobCardUpdateManyWithWhereWithoutCustomerInput[]
    deleteMany?: JobCardScalarWhereInput | JobCardScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutJobCardsInput = {
    create?: XOR<UserCreateWithoutJobCardsInput, UserUncheckedCreateWithoutJobCardsInput>
    connectOrCreate?: UserCreateOrConnectWithoutJobCardsInput
    connect?: UserWhereUniqueInput
  }

  export type CustomerCreateNestedOneWithoutJobCardsInput = {
    create?: XOR<CustomerCreateWithoutJobCardsInput, CustomerUncheckedCreateWithoutJobCardsInput>
    connectOrCreate?: CustomerCreateOrConnectWithoutJobCardsInput
    connect?: CustomerWhereUniqueInput
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type NullableFloatFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type UserUpdateOneRequiredWithoutJobCardsNestedInput = {
    create?: XOR<UserCreateWithoutJobCardsInput, UserUncheckedCreateWithoutJobCardsInput>
    connectOrCreate?: UserCreateOrConnectWithoutJobCardsInput
    upsert?: UserUpsertWithoutJobCardsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutJobCardsInput, UserUpdateWithoutJobCardsInput>, UserUncheckedUpdateWithoutJobCardsInput>
  }

  export type CustomerUpdateOneWithoutJobCardsNestedInput = {
    create?: XOR<CustomerCreateWithoutJobCardsInput, CustomerUncheckedCreateWithoutJobCardsInput>
    connectOrCreate?: CustomerCreateOrConnectWithoutJobCardsInput
    upsert?: CustomerUpsertWithoutJobCardsInput
    disconnect?: CustomerWhereInput | boolean
    delete?: CustomerWhereInput | boolean
    connect?: CustomerWhereUniqueInput
    update?: XOR<XOR<CustomerUpdateToOneWithWhereWithoutJobCardsInput, CustomerUpdateWithoutJobCardsInput>, CustomerUncheckedUpdateWithoutJobCardsInput>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    search?: string
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type JobCardCreateWithoutCreatedByInput = {
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    customer?: CustomerCreateNestedOneWithoutJobCardsInput
  }

  export type JobCardUncheckedCreateWithoutCreatedByInput = {
    id?: number
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    customerId?: number | null
  }

  export type JobCardCreateOrConnectWithoutCreatedByInput = {
    where: JobCardWhereUniqueInput
    create: XOR<JobCardCreateWithoutCreatedByInput, JobCardUncheckedCreateWithoutCreatedByInput>
  }

  export type JobCardCreateManyCreatedByInputEnvelope = {
    data: JobCardCreateManyCreatedByInput | JobCardCreateManyCreatedByInput[]
    skipDuplicates?: boolean
  }

  export type JobCardUpsertWithWhereUniqueWithoutCreatedByInput = {
    where: JobCardWhereUniqueInput
    update: XOR<JobCardUpdateWithoutCreatedByInput, JobCardUncheckedUpdateWithoutCreatedByInput>
    create: XOR<JobCardCreateWithoutCreatedByInput, JobCardUncheckedCreateWithoutCreatedByInput>
  }

  export type JobCardUpdateWithWhereUniqueWithoutCreatedByInput = {
    where: JobCardWhereUniqueInput
    data: XOR<JobCardUpdateWithoutCreatedByInput, JobCardUncheckedUpdateWithoutCreatedByInput>
  }

  export type JobCardUpdateManyWithWhereWithoutCreatedByInput = {
    where: JobCardScalarWhereInput
    data: XOR<JobCardUpdateManyMutationInput, JobCardUncheckedUpdateManyWithoutCreatedByInput>
  }

  export type JobCardScalarWhereInput = {
    AND?: JobCardScalarWhereInput | JobCardScalarWhereInput[]
    OR?: JobCardScalarWhereInput[]
    NOT?: JobCardScalarWhereInput | JobCardScalarWhereInput[]
    id?: IntFilter<"JobCard"> | number
    billNo?: IntNullableFilter<"JobCard"> | number | null
    date?: DateTimeFilter<"JobCard"> | Date | string
    isOn?: BoolFilter<"JobCard"> | boolean
    isOff?: BoolFilter<"JobCard"> | boolean
    hasBattery?: BoolFilter<"JobCard"> | boolean
    hasDoor?: BoolFilter<"JobCard"> | boolean
    hasSim?: BoolFilter<"JobCard"> | boolean
    hasSlot?: BoolFilter<"JobCard"> | boolean
    customerName?: StringFilter<"JobCard"> | string
    address?: StringNullableFilter<"JobCard"> | string | null
    mobileNumber?: StringFilter<"JobCard"> | string
    complaint?: StringFilter<"JobCard"> | string
    model?: StringFilter<"JobCard"> | string
    admissionFees?: FloatNullableFilter<"JobCard"> | number | null
    aadhaarNumber?: StringNullableFilter<"JobCard"> | string | null
    estimate?: FloatNullableFilter<"JobCard"> | number | null
    advance?: FloatNullableFilter<"JobCard"> | number | null
    finalAmount?: FloatNullableFilter<"JobCard"> | number | null
    status?: StringFilter<"JobCard"> | string
    createdAt?: DateTimeFilter<"JobCard"> | Date | string
    updatedAt?: DateTimeFilter<"JobCard"> | Date | string
    userId?: IntFilter<"JobCard"> | number
    customerId?: IntNullableFilter<"JobCard"> | number | null
  }

  export type JobCardCreateWithoutCustomerInput = {
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    createdBy: UserCreateNestedOneWithoutJobCardsInput
  }

  export type JobCardUncheckedCreateWithoutCustomerInput = {
    id?: number
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    userId: number
  }

  export type JobCardCreateOrConnectWithoutCustomerInput = {
    where: JobCardWhereUniqueInput
    create: XOR<JobCardCreateWithoutCustomerInput, JobCardUncheckedCreateWithoutCustomerInput>
  }

  export type JobCardCreateManyCustomerInputEnvelope = {
    data: JobCardCreateManyCustomerInput | JobCardCreateManyCustomerInput[]
    skipDuplicates?: boolean
  }

  export type JobCardUpsertWithWhereUniqueWithoutCustomerInput = {
    where: JobCardWhereUniqueInput
    update: XOR<JobCardUpdateWithoutCustomerInput, JobCardUncheckedUpdateWithoutCustomerInput>
    create: XOR<JobCardCreateWithoutCustomerInput, JobCardUncheckedCreateWithoutCustomerInput>
  }

  export type JobCardUpdateWithWhereUniqueWithoutCustomerInput = {
    where: JobCardWhereUniqueInput
    data: XOR<JobCardUpdateWithoutCustomerInput, JobCardUncheckedUpdateWithoutCustomerInput>
  }

  export type JobCardUpdateManyWithWhereWithoutCustomerInput = {
    where: JobCardScalarWhereInput
    data: XOR<JobCardUpdateManyMutationInput, JobCardUncheckedUpdateManyWithoutCustomerInput>
  }

  export type UserCreateWithoutJobCardsInput = {
    username: string
    password: string
    name?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUncheckedCreateWithoutJobCardsInput = {
    id?: number
    username: string
    password: string
    name?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserCreateOrConnectWithoutJobCardsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutJobCardsInput, UserUncheckedCreateWithoutJobCardsInput>
  }

  export type CustomerCreateWithoutJobCardsInput = {
    name: string
    mobileNumber: string
    address?: string | null
    aadhaarNumber?: string | null
    visitCount?: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type CustomerUncheckedCreateWithoutJobCardsInput = {
    id?: number
    name: string
    mobileNumber: string
    address?: string | null
    aadhaarNumber?: string | null
    visitCount?: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type CustomerCreateOrConnectWithoutJobCardsInput = {
    where: CustomerWhereUniqueInput
    create: XOR<CustomerCreateWithoutJobCardsInput, CustomerUncheckedCreateWithoutJobCardsInput>
  }

  export type UserUpsertWithoutJobCardsInput = {
    update: XOR<UserUpdateWithoutJobCardsInput, UserUncheckedUpdateWithoutJobCardsInput>
    create: XOR<UserCreateWithoutJobCardsInput, UserUncheckedCreateWithoutJobCardsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutJobCardsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutJobCardsInput, UserUncheckedUpdateWithoutJobCardsInput>
  }

  export type UserUpdateWithoutJobCardsInput = {
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateWithoutJobCardsInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CustomerUpsertWithoutJobCardsInput = {
    update: XOR<CustomerUpdateWithoutJobCardsInput, CustomerUncheckedUpdateWithoutJobCardsInput>
    create: XOR<CustomerCreateWithoutJobCardsInput, CustomerUncheckedCreateWithoutJobCardsInput>
    where?: CustomerWhereInput
  }

  export type CustomerUpdateToOneWithWhereWithoutJobCardsInput = {
    where?: CustomerWhereInput
    data: XOR<CustomerUpdateWithoutJobCardsInput, CustomerUncheckedUpdateWithoutJobCardsInput>
  }

  export type CustomerUpdateWithoutJobCardsInput = {
    name?: StringFieldUpdateOperationsInput | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    visitCount?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CustomerUncheckedUpdateWithoutJobCardsInput = {
    id?: IntFieldUpdateOperationsInput | number
    name?: StringFieldUpdateOperationsInput | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    visitCount?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type JobCardCreateManyCreatedByInput = {
    id?: number
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    customerId?: number | null
  }

  export type JobCardUpdateWithoutCreatedByInput = {
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    customer?: CustomerUpdateOneWithoutJobCardsNestedInput
  }

  export type JobCardUncheckedUpdateWithoutCreatedByInput = {
    id?: IntFieldUpdateOperationsInput | number
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    customerId?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type JobCardUncheckedUpdateManyWithoutCreatedByInput = {
    id?: IntFieldUpdateOperationsInput | number
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    customerId?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type JobCardCreateManyCustomerInput = {
    id?: number
    billNo?: number | null
    date?: Date | string
    isOn?: boolean
    isOff?: boolean
    hasBattery?: boolean
    hasDoor?: boolean
    hasSim?: boolean
    hasSlot?: boolean
    customerName: string
    address?: string | null
    mobileNumber: string
    complaint: string
    model: string
    admissionFees?: number | null
    aadhaarNumber?: string | null
    estimate?: number | null
    advance?: number | null
    finalAmount?: number | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    userId: number
  }

  export type JobCardUpdateWithoutCustomerInput = {
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    createdBy?: UserUpdateOneRequiredWithoutJobCardsNestedInput
  }

  export type JobCardUncheckedUpdateWithoutCustomerInput = {
    id?: IntFieldUpdateOperationsInput | number
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: IntFieldUpdateOperationsInput | number
  }

  export type JobCardUncheckedUpdateManyWithoutCustomerInput = {
    id?: IntFieldUpdateOperationsInput | number
    billNo?: NullableIntFieldUpdateOperationsInput | number | null
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    isOn?: BoolFieldUpdateOperationsInput | boolean
    isOff?: BoolFieldUpdateOperationsInput | boolean
    hasBattery?: BoolFieldUpdateOperationsInput | boolean
    hasDoor?: BoolFieldUpdateOperationsInput | boolean
    hasSim?: BoolFieldUpdateOperationsInput | boolean
    hasSlot?: BoolFieldUpdateOperationsInput | boolean
    customerName?: StringFieldUpdateOperationsInput | string
    address?: NullableStringFieldUpdateOperationsInput | string | null
    mobileNumber?: StringFieldUpdateOperationsInput | string
    complaint?: StringFieldUpdateOperationsInput | string
    model?: StringFieldUpdateOperationsInput | string
    admissionFees?: NullableFloatFieldUpdateOperationsInput | number | null
    aadhaarNumber?: NullableStringFieldUpdateOperationsInput | string | null
    estimate?: NullableFloatFieldUpdateOperationsInput | number | null
    advance?: NullableFloatFieldUpdateOperationsInput | number | null
    finalAmount?: NullableFloatFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: IntFieldUpdateOperationsInput | number
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}