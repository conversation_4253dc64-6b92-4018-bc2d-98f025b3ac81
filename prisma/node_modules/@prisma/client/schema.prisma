generator client {
  provider      = "prisma-client-js"
  output        = "./node_modules/@prisma/client"
  binaryTargets = ["native", "rhel-openssl-1.0.x"]
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model User {
  id        Int       @id @default(autoincrement())
  username  String    @unique
  password  String
  name      String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  jobCards  JobCard[]
}

model Customer {
  id            Int       @id @default(autoincrement())
  name          String
  mobileNumber  String    @unique
  address       String?
  aadhaarNumber String?
  visitCount    Int       @default(1)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  jobCards      JobCard[]
}

model JobCard {
  id            Int       @id @default(autoincrement())
  billNo        Int?      @unique
  date          DateTime  @default(now())
  isOn          Boolean   @default(false)
  isOff         <PERSON>   @default(false)
  hasBattery    <PERSON>olean   @default(false)
  hasD<PERSON>   @default(false)
  hasS<PERSON>   @default(false)
  hasSlot       <PERSON><PERSON>   @default(false)
  customerName  String
  address       String?
  mobileNumber  String
  complaint     String
  model         String
  admissionFees Float?
  aadhaarNumber String?
  estimate      Float?
  advance       Float?
  finalAmount   Float?
  status        String    @default("pending")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  userId        Int
  createdBy     User      @relation(fields: [userId], references: [id])
  customerId    Int?
  customer      Customer? @relation(fields: [customerId], references: [id])

  // Add indexes for better performance with relationMode = "prisma"
  @@index([userId])
  @@index([customerId])
}
