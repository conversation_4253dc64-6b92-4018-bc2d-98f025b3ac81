#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up production environment variables
 * Run this script with your production URL as an argument
 * 
 * Usage: node setup-production.js https://your-app.vercel.app
 */

const fs = require('fs');
const path = require('path');

function setupProduction(productionUrl) {
  if (!productionUrl) {
    console.error('❌ Error: Please provide your production URL');
    console.log('Usage: node setup-production.js https://your-app.vercel.app');
    process.exit(1);
  }

  // Validate URL
  try {
    new URL(productionUrl);
  } catch (error) {
    console.error('❌ Error: Invalid URL provided');
    console.log('Please provide a valid URL like: https://your-app.vercel.app');
    process.exit(1);
  }

  console.log(`🚀 Setting up production environment for: ${productionUrl}`);

  // Update .env.production
  const envProductionPath = path.join(__dirname, '.env.production');
  const envProductionContent = `
# Environment variables for production deployment
NEXT_DISABLE_STATIC_GENERATION=true
NEXT_STATIC_GENERATION_TIMEOUT=60

# Production URLs
NEXT_PUBLIC_API_BASE_URL=${productionUrl}
NEXTAUTH_URL=${productionUrl}
NEXTAUTH_SECRET=a-more-secure-secret-key-for-jwt-encryption-123456789
NEXTAUTH_DEBUG=false

# Database URL - This should be set in your deployment platform's environment variables
# DATABASE_URL=your-neon-postgresql-connection-string
`;

  fs.writeFileSync(envProductionPath, envProductionContent);
  console.log('✅ Updated .env.production');

  // Update vercel.json if it exists
  const vercelJsonPath = path.join(__dirname, 'vercel.json');
  if (fs.existsSync(vercelJsonPath)) {
    const vercelConfig = JSON.parse(fs.readFileSync(vercelJsonPath, 'utf8'));
    
    // Update environment variables in vercel.json
    if (!vercelConfig.env) {
      vercelConfig.env = {};
    }
    
    vercelConfig.env.NEXT_PUBLIC_API_BASE_URL = productionUrl;
    vercelConfig.env.NEXTAUTH_URL = productionUrl;
    
    fs.writeFileSync(vercelJsonPath, JSON.stringify(vercelConfig, null, 2));
    console.log('✅ Updated vercel.json');
  }

  console.log('\n🎉 Production setup complete!');
  console.log('\n📋 Next steps:');
  console.log('1. If deploying to Vercel, set these environment variables in your Vercel dashboard:');
  console.log(`   - NEXT_PUBLIC_API_BASE_URL=${productionUrl}`);
  console.log(`   - NEXTAUTH_URL=${productionUrl}`);
  console.log('   - DATABASE_URL=your-neon-postgresql-connection-string');
  console.log('   - NEXTAUTH_SECRET=a-more-secure-secret-key-for-jwt-encryption-123456789');
  console.log('\n2. Deploy your application');
  console.log('\n3. Your app will now fetch data from the Neon database in production');
}

// Get production URL from command line arguments
const productionUrl = process.argv[2];
setupProduction(productionUrl);
