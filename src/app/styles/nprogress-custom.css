/* Make clicks pass-through */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
}

/* Fancy blur effect */
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #3b82f6, 0 0 5px #8b5cf6;
  opacity: 1.0;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these styles to get rid of the spinner */
#nprogress .spinner {
  display: none;
}

/* When taking action on a button */
.nprogress-busy button {
  position: relative;
  overflow: hidden;
}

.nprogress-busy button::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  width: 100%;
  background: linear-gradient(to right, #3b82f6, #8b5cf6);
  animation: button-loading 1s infinite;
}

@keyframes button-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
